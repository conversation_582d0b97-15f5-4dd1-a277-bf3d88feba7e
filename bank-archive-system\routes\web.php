<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DocumentController;
use App\Http\Controllers\Admin\DepartmentController;
use App\Http\Controllers\Admin\DocumentCategoryController;
use App\Http\Controllers\Admin\UserController;
use Illuminate\Support\Facades\Route;

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard route
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Admin routes with authentication and permission middleware
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {

    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/chart-data', [DashboardController::class, 'getChartData'])->name('chart-data');

    // Documents management
    Route::resource('documents', DocumentController::class);
    Route::post('documents/{document}/approve', [DocumentController::class, 'approve'])->name('documents.approve');
    Route::post('documents/{document}/reject', [DocumentController::class, 'reject'])->name('documents.reject');
    Route::get('documents/{document}/download', [DocumentController::class, 'download'])->name('documents.download');
    Route::post('documents/{document}/comments', [DocumentController::class, 'addComment'])->name('documents.comments.store');

    // Departments management
    Route::resource('departments', DepartmentController::class);

    // Document categories management
    Route::resource('categories', DocumentCategoryController::class);

    // Users management
    Route::resource('users', UserController::class);
    Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::post('users/{user}/assign-role', [UserController::class, 'assignRole'])->name('users.assign-role');

    // Search and filters
    Route::get('search', [DocumentController::class, 'search'])->name('search');
    Route::get('advanced-search', [DocumentController::class, 'advancedSearch'])->name('advanced-search');

    // Reports
    Route::get('reports', [DashboardController::class, 'reports'])->name('reports');
    Route::get('reports/export', [DashboardController::class, 'exportReport'])->name('reports.export');

    // Audit logs
    Route::get('audit-logs', [DashboardController::class, 'auditLogs'])->name('audit-logs');

    // System settings
    Route::get('settings', [DashboardController::class, 'settings'])->name('settings');
    Route::post('settings', [DashboardController::class, 'updateSettings'])->name('settings.update');

    // Backup and restore
    Route::post('backup', [DashboardController::class, 'backup'])->name('backup');
    Route::post('restore', [DashboardController::class, 'restore'])->name('restore');
});

// Profile management
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Language switching
Route::get('/language/{locale}', function ($locale) {
    if (in_array($locale, ['ar', 'en'])) {
        session(['locale' => $locale]);
        app()->setLocale($locale);

        // Update user preference if authenticated
        if (auth()->check()) {
            auth()->user()->update(['preferred_language' => $locale]);
        }
    }
    return redirect()->back();
})->name('language.switch');

require __DIR__.'/auth.php';
