# منظومة الأرشفة الإلكترونية للمؤسسات المصرفية
# Electronic Archive System for Banking Institutions

## نظرة عامة | Overview

منظومة أرشفة إلكترونية احترافية مطورة خصيصاً للمؤسسات المصرفية الكبيرة والإقليمية باستخدام <PERSON>. تدعم اللغتين العربية والإنجليزية مع تصميم عصري ومتجاوب.

A professional electronic archive system developed specifically for large and regional banking institutions using Laravel. Supports both Arabic and English languages with modern and responsive design.

## المميزات الرئيسية | Key Features

### 🔐 نظام صلاحيات متقدم | Advanced Permission System
- صلاحيات متعددة المستويات (عرض، إضافة، تعديل، حذف، تحميل، مشاركة، اعتماد)
- أدوار مخصصة لكل قسم (الإدارة، الموارد البشرية، الشؤون القانونية، المالية، إلخ)
- نظام أدوار قابل للتخصيص

### 📁 إدارة المستندات | Document Management
- تصنيف المستندات حسب النوع والقسم والتاريخ
- رفع ملفات متعددة الأنواع (PDF، صور، Word، إلخ)
- معاينة مباشرة للملفات داخل النظام
- بحث متقدم بالكلمات المفتاحية والتواريخ والأقسام
- إدارة ديناميكية للمجلدات والمسارات

### 🔍 البحث والتصفية | Search & Filtering
- بحث متقدم متعدد المعايير
- تصفية حسب القسم والفئة والتاريخ
- بحث بالكلمات المفتاحية
- حفظ عمليات البحث المفضلة

### 📊 التقارير والإحصائيات | Reports & Analytics
- لوحة تحكم تفاعلية مع إحصائيات دقيقة
- تقارير مفصلة قابلة للتصدير
- رسوم بيانية تفاعلية
- مراقبة استخدام النظام

### 🔒 الأمان والحماية | Security & Protection
- تشفير الملفات والبيانات
- حماية من SQL Injection وCSRF وXSS
- تسجيل جميع العمليات (Audit Trail)
- نسخ احتياطي تلقائي

### 🌐 دعم متعدد اللغات | Multi-language Support
- واجهة باللغتين العربية والإنجليزية
- تبديل اللغة الفوري
- دعم RTL للعربية

## متطلبات النظام | System Requirements

- PHP 8.2 أو أحدث | PHP 8.2 or higher
- Composer
- Node.js & NPM
- MySQL 8.0 أو أحدث | MySQL 8.0 or higher
- Apache/Nginx

## التثبيت | Installation

### 1. استنساخ المشروع | Clone the Project
```bash
git clone <repository-url>
cd bank-archive-system
```

### 2. تثبيت التبعيات | Install Dependencies
```bash
# تثبيت تبعيات PHP | Install PHP dependencies
composer install

# تثبيت تبعيات Node.js | Install Node.js dependencies
npm install
```

### 3. إعداد البيئة | Environment Setup
```bash
# نسخ ملف البيئة | Copy environment file
cp .env.example .env

# توليد مفتاح التطبيق | Generate application key
php artisan key:generate
```

### 4. إعداد قاعدة البيانات | Database Configuration
قم بتحديث ملف `.env` بمعلومات قاعدة البيانات:
Update `.env` file with your database information:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bank_archive_system
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. تشغيل الهجرات والبذور | Run Migrations & Seeders
```bash
# تشغيل الهجرات مع البذور | Run migrations with seeders
php artisan migrate:fresh --seed
```

### 6. إنشاء رابط التخزين | Create Storage Link
```bash
php artisan storage:link
```

### 7. بناء الأصول | Build Assets
```bash
# للتطوير | For development
npm run dev

# للإنتاج | For production
npm run build
```

### 8. تشغيل الخادم | Start the Server
```bash
php artisan serve
```

## بيانات الدخول الافتراضية | Default Login Credentials

### مدير النظام | Super Admin
- **البريد الإلكتروني | Email:** <EMAIL>
- **كلمة المرور | Password:** password

### مدير الموارد البشرية | HR Manager
- **البريد الإلكتروني | Email:** <EMAIL>
- **كلمة المرور | Password:** password

### مدير الشؤون القانونية | Legal Manager
- **البريد الإلكتروني | Email:** <EMAIL>
- **كلمة المرور | Password:** password

### مدير الشؤون المالية | Finance Manager
- **البريد الإلكتروني | Email:** <EMAIL>
- **كلمة المرور | Password:** password

### موظف عادي | Regular Employee
- **البريد الإلكتروني | Email:** <EMAIL>
- **كلمة المرور | Password:** password

## الأدوار والصلاحيات | Roles & Permissions

### مدير عام | Super Admin
- صلاحية كاملة على جميع أجزاء النظام
- إدارة المستخدمين والأدوار
- إعدادات النظام والنسخ الاحتياطي

### مدير | Admin
- إدارة المستندات والأقسام والفئات
- إدارة المستخدمين (محدود)
- عرض التقارير وسجل العمليات

### مدير قسم | Department Manager
- إدارة مستندات القسم
- اعتماد المستندات
- إدارة موظفي القسم

### موظف | Employee
- عرض وإنشاء المستندات
- تحميل المستندات المسموحة
- إضافة تعليقات

## الحزم المستخدمة | Used Packages

- **Laravel Framework 12.x** - إطار العمل الأساسي
- **Spatie Laravel Permission** - إدارة الأدوار والصلاحيات
- **Spatie Laravel Media Library** - إدارة الملفات والوسائط
- **Spatie Laravel Activity Log** - تسجيل العمليات
- **Intervention Image** - معالجة الصور
- **Laravel Breeze** - نظام المصادقة
- **Livewire** - مكونات تفاعلية
- **Tailwind CSS** - تصميم الواجهات
- **Alpine.js** - تفاعل JavaScript
- **Chart.js** - الرسوم البيانية

## هيكل المشروع | Project Structure

```
bank-archive-system/
├── app/
│   ├── Http/Controllers/Admin/    # تحكم الإدارة
│   ├── Models/                    # النماذج
│   ├── Providers/                 # مقدمي الخدمة
│   └── Http/Middleware/           # الوسطاء
├── database/
│   ├── migrations/                # هجرات قاعدة البيانات
│   └── seeders/                   # بذور البيانات
├── resources/
│   ├── views/                     # ملفات العرض
│   ├── lang/                      # ملفات اللغة
│   └── js/                        # ملفات JavaScript
└── routes/                        # ملفات التوجيه
```

## الدعم والمساعدة | Support & Help

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

For support or to report issues, please contact the development team.

## الترخيص | License

هذا المشروع مرخص تحت رخصة MIT.

This project is licensed under the MIT License.

---

**تم التطوير بواسطة | Developed by:** Augment Agent  
**التاريخ | Date:** 2025  
**الإصدار | Version:** 1.0.0

## الوصول للنظام | System Access

بعد تشغيل الخادم، يمكنك الوصول للنظام عبر:
After starting the server, you can access the system at:

**http://127.0.0.1:8000**

سيتم توجيهك تلقائياً إلى صفحة تسجيل الدخول.
You will be automatically redirected to the login page.
