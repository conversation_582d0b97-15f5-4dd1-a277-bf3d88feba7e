<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Document;
use App\Models\Department;
use App\Models\DocumentCategory;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Statistics
        $stats = [
            'total_documents' => Document::count(),
            'active_documents' => Document::where('status', 'active')->count(),
            'confidential_documents' => Document::where('is_confidential', true)->count(),
            'pending_approval' => Document::where('requires_approval', true)
                                         ->whereNull('approved_by')
                                         ->count(),
            'total_departments' => Department::where('is_active', true)->count(),
            'total_categories' => DocumentCategory::where('is_active', true)->count(),
            'total_users' => User::where('is_active', true)->count(),
            'expired_documents' => Document::whereNotNull('expiry_date')
                                         ->where('expiry_date', '<', now())
                                         ->count(),
        ];

        // Recent documents
        $recentDocuments = Document::with(['department', 'category', 'uploader'])
                                  ->latest()
                                  ->limit(10)
                                  ->get();

        // Documents by department
        $documentsByDepartment = Department::withCount('documents')
                                          ->where('is_active', true)
                                          ->get()
                                          ->map(function ($dept) {
                                              return [
                                                  'name' => app()->getLocale() === 'ar' ? $dept->name_ar : $dept->name_en,
                                                  'count' => $dept->documents_count
                                              ];
                                          });

        // Documents by category
        $documentsByCategory = DocumentCategory::withCount('documents')
                                              ->where('is_active', true)
                                              ->get()
                                              ->map(function ($cat) {
                                                  return [
                                                      'name' => app()->getLocale() === 'ar' ? $cat->name_ar : $cat->name_en,
                                                      'count' => $cat->documents_count,
                                                      'color' => $cat->color
                                                  ];
                                              });

        // Monthly document uploads (last 12 months)
        $monthlyUploads = collect();
        if (config('database.default') === 'sqlite') {
            // SQLite compatible query
            $monthlyUploads = Document::select(
                                        DB::raw('strftime("%Y", created_at) as year'),
                                        DB::raw('strftime("%m", created_at) as month'),
                                        DB::raw('COUNT(*) as count')
                                      )
                                      ->where('created_at', '>=', Carbon::now()->subMonths(12))
                                      ->groupBy('year', 'month')
                                      ->orderBy('year', 'asc')
                                      ->orderBy('month', 'asc')
                                      ->get()
                                      ->map(function ($item) {
                                          return [
                                              'month' => Carbon::create($item->year, $item->month)->format('M Y'),
                                              'count' => $item->count
                                          ];
                                      });
        } else {
            // MySQL compatible query
            $monthlyUploads = Document::select(
                                        DB::raw('YEAR(created_at) as year'),
                                        DB::raw('MONTH(created_at) as month'),
                                        DB::raw('COUNT(*) as count')
                                      )
                                      ->where('created_at', '>=', Carbon::now()->subMonths(12))
                                      ->groupBy('year', 'month')
                                      ->orderBy('year', 'asc')
                                      ->orderBy('month', 'asc')
                                      ->get()
                                      ->map(function ($item) {
                                          return [
                                              'month' => Carbon::create($item->year, $item->month)->format('M Y'),
                                              'count' => $item->count
                                          ];
                                      });
        }

        // Storage usage (if using media library)
        $storageUsage = [
            'total_files' => 0,
            'total_size' => 0,
        ];

        if (class_exists('\Spatie\MediaLibrary\MediaCollections\Models\Media')) {
            $media = \Spatie\MediaLibrary\MediaCollections\Models\Media::all();
            $storageUsage['total_files'] = $media->count();
            $storageUsage['total_size'] = $media->sum('size');
        }

        return view('dashboard', compact(
            'stats',
            'recentDocuments',
            'documentsByDepartment',
            'documentsByCategory',
            'monthlyUploads',
            'storageUsage'
        ));
    }

    public function getChartData(Request $request)
    {
        $type = $request->get('type', 'monthly');

        switch ($type) {
            case 'monthly':
                return $this->getMonthlyData();
            case 'department':
                return $this->getDepartmentData();
            case 'category':
                return $this->getCategoryData();
            default:
                return response()->json(['error' => 'Invalid chart type'], 400);
        }
    }

    private function getMonthlyData()
    {
        if (config('database.default') === 'sqlite') {
            // SQLite compatible query
            $data = Document::select(
                        DB::raw('strftime("%Y", created_at) as year'),
                        DB::raw('strftime("%m", created_at) as month'),
                        DB::raw('COUNT(*) as count')
                    )
                    ->where('created_at', '>=', Carbon::now()->subMonths(12))
                    ->groupBy('year', 'month')
                    ->orderBy('year', 'asc')
                    ->orderBy('month', 'asc')
                    ->get()
                    ->map(function ($item) {
                        return [
                            'label' => Carbon::create($item->year, $item->month)->format('M Y'),
                            'value' => $item->count
                        ];
                    });
        } else {
            // MySQL compatible query
            $data = Document::select(
                        DB::raw('YEAR(created_at) as year'),
                        DB::raw('MONTH(created_at) as month'),
                        DB::raw('COUNT(*) as count')
                    )
                    ->where('created_at', '>=', Carbon::now()->subMonths(12))
                    ->groupBy('year', 'month')
                    ->orderBy('year', 'asc')
                    ->orderBy('month', 'asc')
                    ->get()
                    ->map(function ($item) {
                        return [
                            'label' => Carbon::create($item->year, $item->month)->format('M Y'),
                            'value' => $item->count
                        ];
                    });
        }

        return response()->json($data);
    }

    private function getDepartmentData()
    {
        $data = Department::withCount('documents')
                          ->where('is_active', true)
                          ->get()
                          ->map(function ($dept) {
                              return [
                                  'label' => app()->getLocale() === 'ar' ? $dept->name_ar : $dept->name_en,
                                  'value' => $dept->documents_count
                              ];
                          });

        return response()->json($data);
    }

    private function getCategoryData()
    {
        $data = DocumentCategory::withCount('documents')
                                ->where('is_active', true)
                                ->get()
                                ->map(function ($cat) {
                                    return [
                                        'label' => app()->getLocale() === 'ar' ? $cat->name_ar : $cat->name_en,
                                        'value' => $cat->documents_count,
                                        'color' => $cat->color
                                    ];
                                });

        return response()->json($data);
    }
}
