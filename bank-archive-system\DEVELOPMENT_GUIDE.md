# دليل التطوير | Development Guide

## نظرة عامة | Overview

هذا الدليل يوضح كيفية تطوير وتوسيع منظومة الأرشفة الإلكترونية.

This guide explains how to develop and extend the electronic archive system.

## هيكل قاعدة البيانات | Database Structure

### الجداول الرئيسية | Main Tables

#### 1. المستخدمين | Users (`users`)
```sql
- id: معرف المستخدم
- name: اسم المستخدم
- email: البريد الإلكتروني
- employee_id: رقم الموظف
- department_id: معرف القسم
- phone: رقم الهاتف
- position_ar: المنصب بالعربية
- position_en: المنصب بالإنجليزية
- is_active: حالة النشاط
- preferred_language: اللغة المفضلة
```

#### 2. الأقسام | Departments (`departments`)
```sql
- id: معرف القسم
- name_ar: الاسم بالعربية
- name_en: الاسم بالإنجليزية
- description_ar: الوصف بالعربية
- description_en: الوصف بالإنجليزية
- code: رمز القسم
- is_active: حالة النشاط
```

#### 3. فئات المستندات | Document Categories (`document_categories`)
```sql
- id: معرف الفئة
- name_ar: الاسم بالعربية
- name_en: الاسم بالإنجليزية
- description_ar: الوصف بالعربية
- description_en: الوصف بالإنجليزية
- code: رمز الفئة
- color: لون الفئة
- is_active: حالة النشاط
```

#### 4. المستندات | Documents (`documents`)
```sql
- id: معرف المستند
- title_ar: العنوان بالعربية
- title_en: العنوان بالإنجليزية
- description_ar: الوصف بالعربية
- description_en: الوصف بالإنجليزية
- document_number: رقم المستند
- department_id: معرف القسم
- category_id: معرف الفئة
- uploaded_by: معرف المستخدم الذي رفع المستند
- status: حالة المستند
- keywords: الكلمات المفتاحية (JSON)
- document_date: تاريخ المستند
- expiry_date: تاريخ انتهاء الصلاحية
- is_confidential: هل المستند سري
- requires_approval: هل يتطلب موافقة
- approved_by: معرف المستخدم الذي اعتمد المستند
- approved_at: تاريخ الاعتماد
```

#### 5. تعليقات المستندات | Document Comments (`document_comments`)
```sql
- id: معرف التعليق
- document_id: معرف المستند
- user_id: معرف المستخدم
- comment: نص التعليق
- type: نوع التعليق (comment, approval_request, signature_request)
- is_internal: هل التعليق داخلي
```

## إضافة ميزات جديدة | Adding New Features

### 1. إنشاء Controller جديد | Creating New Controller
```bash
php artisan make:controller Admin/NewFeatureController --resource
```

### 2. إنشاء Model جديد | Creating New Model
```bash
php artisan make:model NewModel -m
```

### 3. إنشاء Migration جديد | Creating New Migration
```bash
php artisan make:migration create_new_table
```

### 4. إنشاء Seeder جديد | Creating New Seeder
```bash
php artisan make:seeder NewSeeder
```

## إضافة صلاحيات جديدة | Adding New Permissions

### 1. تحديث RolePermissionSeeder
```php
// في ملف database/seeders/RolePermissionSeeder.php
$permissions = [
    // الصلاحيات الموجودة...
    'new_feature.view',
    'new_feature.create',
    'new_feature.edit',
    'new_feature.delete',
];
```

### 2. تحديث ملفات اللغة
```php
// في ملف resources/lang/ar/messages.php
'permissions' => [
    // الصلاحيات الموجودة...
    'new_feature.view' => 'عرض الميزة الجديدة',
    'new_feature.create' => 'إنشاء الميزة الجديدة',
    // ...
];
```

## إضافة لغات جديدة | Adding New Languages

### 1. إنشاء مجلد اللغة
```bash
mkdir resources/lang/fr  # للفرنسية مثلاً
```

### 2. نسخ ملفات اللغة
```bash
cp resources/lang/en/messages.php resources/lang/fr/messages.php
```

### 3. ترجمة المحتوى
قم بترجمة جميع النصوص في الملف الجديد.

### 4. تحديث التطبيق
```php
// في config/app.php
'supported_locales' => ['ar', 'en', 'fr'],
```

## تخصيص التصميم | Customizing Design

### 1. تعديل ألوان Tailwind
```javascript
// في tailwind.config.js
module.exports = {
    theme: {
        extend: {
            colors: {
                'bank-blue': '#1e40af',
                'bank-gold': '#f59e0b',
            }
        }
    }
}
```

### 2. إضافة CSS مخصص
```css
/* في resources/css/app.css */
.custom-bank-style {
    @apply bg-bank-blue text-white rounded-lg p-4;
}
```

## إضافة تقارير جديدة | Adding New Reports

### 1. إنشاء Report Controller
```php
class ReportController extends Controller
{
    public function customReport()
    {
        $data = Document::with(['department', 'category'])
                       ->whereBetween('created_at', [$startDate, $endDate])
                       ->get();
        
        return view('admin.reports.custom', compact('data'));
    }
}
```

### 2. إضافة Route
```php
Route::get('reports/custom', [ReportController::class, 'customReport'])
     ->name('reports.custom');
```

## إعداد النسخ الاحتياطي | Backup Configuration

### 1. تثبيت حزمة النسخ الاحتياطي
```bash
composer require spatie/laravel-backup
```

### 2. نشر ملف التكوين
```bash
php artisan vendor:publish --provider="Spatie\Backup\BackupServiceProvider"
```

### 3. إعداد المهام المجدولة
```php
// في app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    $schedule->command('backup:clean')->daily()->at('01:00');
    $schedule->command('backup:run')->daily()->at('02:00');
}
```

## اختبار النظام | Testing the System

### 1. تشغيل الاختبارات
```bash
php artisan test
```

### 2. إنشاء اختبار جديد
```bash
php artisan make:test DocumentTest
```

### 3. مثال على اختبار
```php
public function test_user_can_create_document()
{
    $user = User::factory()->create();
    $this->actingAs($user);
    
    $response = $this->post('/admin/documents', [
        'title_ar' => 'مستند تجريبي',
        'title_en' => 'Test Document',
        // باقي البيانات...
    ]);
    
    $response->assertStatus(302);
    $this->assertDatabaseHas('documents', [
        'title_ar' => 'مستند تجريبي'
    ]);
}
```

## الأمان والحماية | Security & Protection

### 1. تحديث التبعيات
```bash
composer update
npm update
```

### 2. فحص الثغرات الأمنية
```bash
composer audit
npm audit
```

### 3. تفعيل HTTPS في الإنتاج
```php
// في AppServiceProvider
public function boot()
{
    if (app()->environment('production')) {
        URL::forceScheme('https');
    }
}
```

## نشر النظام | Deployment

### 1. إعداد خادم الإنتاج
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade

# تثبيت PHP وMySQL
sudo apt install php8.2 php8.2-fpm php8.2-mysql mysql-server

# تثبيت Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 2. رفع الملفات
```bash
# نسخ الملفات إلى الخادم
rsync -avz --exclude node_modules --exclude .git . user@server:/var/www/bank-archive/
```

### 3. إعداد البيئة
```bash
# في الخادم
cd /var/www/bank-archive
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## مراقبة الأداء | Performance Monitoring

### 1. تفعيل Laravel Telescope (للتطوير فقط)
```bash
composer require laravel/telescope --dev
php artisan telescope:install
```

### 2. مراقبة قاعدة البيانات
```php
// إضافة فهارس للجداول الكبيرة
Schema::table('documents', function (Blueprint $table) {
    $table->index(['status', 'created_at']);
    $table->index(['department_id', 'category_id']);
});
```

### 3. تحسين الاستعلامات
```php
// استخدام Eager Loading
$documents = Document::with(['department', 'category', 'uploader'])->get();

// استخدام Pagination
$documents = Document::paginate(20);
```

## الصيانة | Maintenance

### 1. تنظيف الملفات المؤقتة
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### 2. تحسين قاعدة البيانات
```bash
php artisan migrate
php artisan db:seed --class=UpdateSeeder
```

### 3. مراقبة مساحة التخزين
```bash
# فحص مساحة الملفات المرفوعة
du -sh storage/app/public/
```

---

**ملاحظة:** تأكد من عمل نسخة احتياطية قبل تطبيق أي تغييرات على نظام الإنتاج.

**Note:** Always backup before applying any changes to the production system.
