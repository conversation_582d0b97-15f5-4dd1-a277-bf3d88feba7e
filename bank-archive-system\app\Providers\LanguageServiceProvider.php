<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class LanguageServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Set locale based on session or user preference
        if (session()->has('locale')) {
            app()->setLocale(session('locale'));
        } elseif (auth()->check() && auth()->user()->preferred_language) {
            app()->setLocale(auth()->user()->preferred_language);
            session(['locale' => auth()->user()->preferred_language]);
        }

        // Share locale with all views
        View::share('currentLocale', app()->getLocale());
        View::share('isRtl', app()->getLocale() === 'ar');
    }
}
