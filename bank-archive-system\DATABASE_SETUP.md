# إعداد قاعدة البيانات | Database Setup

## الوضع الحالي | Current Status

النظام يعمل حالياً مع **SQLite** وهو مناسب للتطوير والاختبار. للاستخدام في الإنتاج، يُنصح بالتبديل إلى **MySQL**.

The system currently works with **SQLite** which is suitable for development and testing. For production use, it's recommended to switch to **MySQL**.

## خيارات قاعدة البيانات | Database Options

### 1. SQLite (الحالي | Current)
✅ **المميزات | Advantages:**
- سهل الإعداد
- لا يحتاج خادم منفصل
- مناسب للتطوير والاختبار
- يعمل فوراً بدون إعداد إضافي

⚠️ **القيود | Limitations:**
- أداء أقل مع البيانات الكبيرة
- لا يدعم الاتصالات المتزامنة الكثيرة
- محدود في الاستعلامات المعقدة

### 2. MySQL (موصى للإنتاج | Recommended for Production)
✅ **المميزات | Advantages:**
- أداء عالي مع البيانات الكبيرة
- يدعم الاتصالات المتزامنة
- مناسب للبيئة المصرفية
- دعم كامل لجميع الاستعلامات

## التبديل إلى MySQL | Switching to MySQL

### 1. تثبيت MySQL | Install MySQL
```bash
# Windows (باستخدام XAMPP أو WAMP)
# أو تحميل MySQL من الموقع الرسمي

# Linux
sudo apt update
sudo apt install mysql-server

# macOS
brew install mysql
```

### 2. إنشاء قاعدة البيانات | Create Database
```sql
-- تسجيل الدخول إلى MySQL
mysql -u root -p

-- إنشاء قاعدة البيانات
CREATE DATABASE bank_archive_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم مخصص
CREATE USER 'archive_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON bank_archive_system.* TO 'archive_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. تحديث ملف .env | Update .env File
```env
# تغيير إعدادات قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bank_archive_system
DB_USERNAME=archive_user
DB_PASSWORD=secure_password
```

### 4. تشغيل الهجرات | Run Migrations
```bash
# حذف قاعدة البيانات الحالية وإعادة إنشائها
php artisan migrate:fresh --seed

# أو تشغيل الهجرات فقط
php artisan migrate
php artisan db:seed
```

## إعداد MySQL للبيئة المصرفية | MySQL Setup for Banking Environment

### 1. إعدادات الأمان | Security Settings
```sql
-- في ملف my.cnf أو my.ini
[mysqld]
# تفعيل SSL
require_secure_transport = ON

# تشفير البيانات
innodb_encrypt_tables = ON

# تسجيل العمليات
general_log = ON
general_log_file = /var/log/mysql/general.log

# حد الاتصالات
max_connections = 200
```

### 2. النسخ الاحتياطي | Backup Configuration
```bash
# نسخ احتياطي يومي
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u archive_user -p bank_archive_system > backup_$DATE.sql

# ضغط النسخة الاحتياطية
gzip backup_$DATE.sql

# حذف النسخ القديمة (أكثر من 30 يوم)
find /backup/path -name "backup_*.sql.gz" -mtime +30 -delete
```

### 3. مراقبة الأداء | Performance Monitoring
```sql
-- فحص حالة قاعدة البيانات
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Queries';
SHOW PROCESSLIST;

-- فحص حجم الجداول
SELECT 
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'bank_archive_system'
ORDER BY (data_length + index_length) DESC;
```

## استكشاف الأخطاء | Troubleshooting

### خطأ الاتصال | Connection Error
```bash
# فحص حالة MySQL
sudo systemctl status mysql

# إعادة تشغيل MySQL
sudo systemctl restart mysql

# فحص السجلات
sudo tail -f /var/log/mysql/error.log
```

### خطأ الصلاحيات | Permission Error
```sql
-- التأكد من صلاحيات المستخدم
SHOW GRANTS FOR 'archive_user'@'localhost';

-- إعادة تعيين الصلاحيات
GRANT ALL PRIVILEGES ON bank_archive_system.* TO 'archive_user'@'localhost';
FLUSH PRIVILEGES;
```

### خطأ الترميز | Encoding Error
```sql
-- التأكد من ترميز قاعدة البيانات
SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'bank_archive_system';

-- تغيير الترميز إذا لزم الأمر
ALTER DATABASE bank_archive_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## اختبار الأداء | Performance Testing

### 1. اختبار الحمولة | Load Testing
```bash
# استخدام Apache Bench
ab -n 1000 -c 10 http://127.0.0.1:8000/dashboard

# استخدام wrk
wrk -t12 -c400 -d30s http://127.0.0.1:8000/dashboard
```

### 2. مراقبة الاستعلامات | Query Monitoring
```php
// في AppServiceProvider.php
use Illuminate\Support\Facades\DB;

public function boot()
{
    if (app()->environment('local')) {
        DB::listen(function ($query) {
            Log::info('Query executed', [
                'sql' => $query->sql,
                'bindings' => $query->bindings,
                'time' => $query->time
            ]);
        });
    }
}
```

## التوصيات | Recommendations

### للتطوير | For Development
- ✅ استخدم SQLite (الإعداد الحالي)
- ✅ سهل وسريع للاختبار
- ✅ لا يحتاج إعداد إضافي

### للإنتاج | For Production
- 🏦 استخدم MySQL أو PostgreSQL
- 🏦 إعداد النسخ الاحتياطي التلقائي
- 🏦 مراقبة الأداء المستمرة
- 🏦 تشفير البيانات الحساسة

### للبيئة المصرفية | For Banking Environment
- 🔒 تفعيل SSL/TLS
- 🔒 تشفير قاعدة البيانات
- 🔒 تسجيل جميع العمليات
- 🔒 نسخ احتياطي متعدد المواقع
- 🔒 مراقبة أمنية 24/7

---

**ملاحظة:** النظام الحالي يعمل بشكل ممتاز مع SQLite للتطوير والاختبار. يمكنك التبديل إلى MySQL لاحقاً عند الحاجة.

**Note:** The current system works excellently with SQLite for development and testing. You can switch to MySQL later when needed.
