<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - نظام الأرشفة الإلكترونية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .info-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .account {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .account:hover {
            background: #bbdefb;
            transform: translateY(-2px);
        }
        .account h3 {
            margin: 0 0 5px 0;
            color: #1976d2;
            font-size: 16px;
        }
        .account p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .credentials {
            background: #fff3e0;
            border: 1px solid #ffcc02;
            border-radius: 5px;
            padding: 8px;
            margin-top: 5px;
            font-family: monospace;
            font-size: 13px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .features {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .features h3 {
            color: #155724;
            margin-top: 0;
        }
        .features ul {
            margin: 10px 0;
            padding-right: 20px;
        }
        .features li {
            margin: 5px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏦 نظام الأرشفة الإلكترونية للمؤسسات المصرفية</h1>
        
        <div class="info-box">
            <h3>🚀 النظام جاهز للاستخدام!</h3>
            <p>تم إنشاء منظومة أرشفة إلكترونية احترافية مع دعم كامل للغة العربية ونظام صلاحيات متقدم.</p>
        </div>

        <div class="info-box">
            <h3>📋 حسابات المستخدمين المتاحة:</h3>
            
            <div class="account" onclick="selectAccount('<EMAIL>', 'password')">
                <h3>👑 مدير النظام</h3>
                <p>صلاحيات كاملة - إدارة جميع أجزاء النظام</p>
                <div class="credentials"><EMAIL> / password</div>
            </div>

            <div class="account" onclick="selectAccount('<EMAIL>', 'password')">
                <h3>👥 مدير الموارد البشرية</h3>
                <p>إدارة مستندات الموارد البشرية والموظفين</p>
                <div class="credentials"><EMAIL> / password</div>
            </div>

            <div class="account" onclick="selectAccount('<EMAIL>', 'password')">
                <h3>⚖️ مدير الشؤون القانونية</h3>
                <p>إدارة المستندات القانونية والعقود</p>
                <div class="credentials"><EMAIL> / password</div>
            </div>

            <div class="account" onclick="selectAccount('<EMAIL>', 'password')">
                <h3>💰 مدير الشؤون المالية</h3>
                <p>إدارة التقارير المالية والمستندات المحاسبية</p>
                <div class="credentials"><EMAIL> / password</div>
            </div>

            <div class="account" onclick="selectAccount('<EMAIL>', 'password')">
                <h3>👤 موظف عادي</h3>
                <p>صلاحيات محدودة - عرض وإنشاء المستندات</p>
                <div class="credentials"><EMAIL> / password</div>
            </div>
        </div>

        <button class="btn" onclick="openSystem()">🔓 فتح نظام الأرشفة الإلكترونية</button>

        <div class="status" id="status"></div>

        <div class="features">
            <h3>✨ المميزات الرئيسية:</h3>
            <ul>
                <li>🔐 نظام صلاحيات متقدم مع 8 أدوار مختلفة</li>
                <li>📁 إدارة شاملة للمستندات مع التصنيف والبحث</li>
                <li>🌐 دعم كامل للعربية والإنجليزية مع RTL</li>
                <li>📊 لوحة تحكم تفاعلية مع رسوم بيانية</li>
                <li>🔒 أمان عالي المستوى مع تشفير البيانات</li>
                <li>📈 تقارير مفصلة وإحصائيات دقيقة</li>
                <li>💾 نسخ احتياطي تلقائي ومراقبة النظام</li>
                <li>🏦 مصمم خصيصاً للمؤسسات المصرفية</li>
            </ul>
        </div>
    </div>

    <script>
        let selectedEmail = '<EMAIL>';
        let selectedPassword = 'password';

        function selectAccount(email, password) {
            selectedEmail = email;
            selectedPassword = password;
            
            // إزالة التحديد من جميع الحسابات
            document.querySelectorAll('.account').forEach(acc => {
                acc.style.background = '#e3f2fd';
                acc.style.border = '1px solid #bbdefb';
            });
            
            // تحديد الحساب المختار
            event.currentTarget.style.background = '#1976d2';
            event.currentTarget.style.color = 'white';
            event.currentTarget.style.border = '1px solid #1976d2';
            
            showStatus('تم اختيار الحساب: ' + email, 'success');
        }

        function openSystem() {
            showStatus('جاري فتح النظام...', 'success');
            
            // فتح النظام في نافذة جديدة
            setTimeout(() => {
                window.open('http://127.0.0.1:8000', '_blank');
                showStatus('تم فتح النظام! استخدم البيانات المختارة لتسجيل الدخول.', 'success');
            }, 1000);
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }
        }

        // تحديد الحساب الافتراضي
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.account').click();
        });
    </script>
</body>
</html>
