<?php

return [
    // General
    'welcome' => 'Welcome',
    'dashboard' => 'Dashboard',
    'home' => 'Home',
    'logout' => 'Logout',
    'login' => 'Login',
    'register' => 'Register',
    'profile' => 'Profile',
    'settings' => 'Settings',
    'search' => 'Search',
    'advanced_search' => 'Advanced Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    'print' => 'Print',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'edit' => 'Edit',
    'view' => 'View',
    'create' => 'Create',
    'update' => 'Update',
    'submit' => 'Submit',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'loading' => 'Loading...',
    'no_data' => 'No data available',
    'confirm' => 'Confirm',
    'yes' => 'Yes',
    'no' => 'No',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'status' => 'Status',
    'actions' => 'Actions',
    'date' => 'Date',
    'time' => 'Time',
    'name' => 'Name',
    'description' => 'Description',
    'code' => 'Code',
    'type' => 'Type',
    'category' => 'Category',
    'department' => 'Department',
    'user' => 'User',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'total' => 'Total',
    'count' => 'Count',
    'size' => 'Size',
    'language' => 'Language',
    'arabic' => 'Arabic',
    'english' => 'English',

    // Navigation
    'nav' => [
        'dashboard' => 'Dashboard',
        'documents' => 'Documents',
        'departments' => 'Departments',
        'categories' => 'Categories',
        'users' => 'Users',
        'reports' => 'Reports',
        'audit_logs' => 'Audit Logs',
        'settings' => 'Settings',
        'backup' => 'Backup',
    ],

    // Documents
    'documents' => [
        'title' => 'Document Management',
        'create' => 'Add New Document',
        'edit' => 'Edit Document',
        'view' => 'View Document',
        'upload' => 'Upload File',
        'download' => 'Download',
        'share' => 'Share',
        'approve' => 'Approve',
        'reject' => 'Reject',
        'pending_approval' => 'Pending Approval',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'confidential' => 'Confidential',
        'public' => 'Public',
        'expired' => 'Expired',
        'document_number' => 'Document Number',
        'document_date' => 'Document Date',
        'expiry_date' => 'Expiry Date',
        'keywords' => 'Keywords',
        'uploaded_by' => 'Uploaded By',
        'approved_by' => 'Approved By',
        'file_size' => 'File Size',
        'file_type' => 'File Type',
        'comments' => 'Comments',
        'add_comment' => 'Add Comment',
        'requires_approval' => 'Requires Approval',
        'preview' => 'Preview',
    ],

    // Departments
    'departments' => [
        'title' => 'Department Management',
        'create' => 'Add New Department',
        'edit' => 'Edit Department',
        'name_ar' => 'Name (Arabic)',
        'name_en' => 'Name (English)',
        'description_ar' => 'Description (Arabic)',
        'description_en' => 'Description (English)',
        'employees_count' => 'Employees Count',
        'documents_count' => 'Documents Count',
    ],

    // Categories
    'categories' => [
        'title' => 'Document Categories Management',
        'create' => 'Add New Category',
        'edit' => 'Edit Category',
        'color' => 'Color',
        'documents_count' => 'Documents Count',
    ],

    // Users
    'users' => [
        'title' => 'User Management',
        'create' => 'Add New User',
        'edit' => 'Edit User',
        'employee_id' => 'Employee ID',
        'position' => 'Position',
        'position_ar' => 'Position (Arabic)',
        'position_en' => 'Position (English)',
        'preferred_language' => 'Preferred Language',
        'role' => 'Role',
        'roles' => 'Roles',
        'permissions' => 'Permissions',
        'assign_role' => 'Assign Role',
        'toggle_status' => 'Toggle Status',
        'last_login' => 'Last Login',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
    ],

    // Dashboard
    'dashboard' => [
        'welcome_message' => 'Welcome to Electronic Archive System',
        'statistics' => 'Statistics',
        'total_documents' => 'Total Documents',
        'active_documents' => 'Active Documents',
        'confidential_documents' => 'Confidential Documents',
        'pending_approval' => 'Pending Approval',
        'expired_documents' => 'Expired Documents',
        'total_departments' => 'Total Departments',
        'total_categories' => 'Total Categories',
        'total_users' => 'Total Users',
        'recent_documents' => 'Recent Documents',
        'documents_by_department' => 'Documents by Department',
        'documents_by_category' => 'Documents by Category',
        'monthly_uploads' => 'Monthly Uploads',
        'storage_usage' => 'Storage Usage',
        'total_files' => 'Total Files',
        'total_size' => 'Total Size',
    ],

    // Messages
    'messages' => [
        'success' => [
            'created' => 'Created successfully',
            'updated' => 'Updated successfully',
            'deleted' => 'Deleted successfully',
            'uploaded' => 'Uploaded successfully',
            'approved' => 'Approved successfully',
            'rejected' => 'Rejected successfully',
            'saved' => 'Saved successfully',
        ],
        'error' => [
            'general' => 'An error occurred, please try again',
            'not_found' => 'Item not found',
            'unauthorized' => 'You are not authorized for this action',
            'validation' => 'Please check the entered data',
            'file_too_large' => 'File size is too large',
            'invalid_file_type' => 'File type is not supported',
        ],
        'confirm' => [
            'delete' => 'Are you sure you want to delete this item?',
            'approve' => 'Are you sure you want to approve this document?',
            'reject' => 'Are you sure you want to reject this document?',
        ],
    ],

    // Validation
    'validation' => [
        'required' => 'This field is required',
        'email' => 'Please enter a valid email address',
        'min' => 'Minimum length should be :min characters',
        'max' => 'Maximum length should be :max characters',
        'unique' => 'This value is already taken',
        'confirmed' => 'Password confirmation does not match',
        'file' => 'Must be a file',
        'mimes' => 'File type is not supported',
        'max_file_size' => 'File size is too large',
    ],

    // Roles
    'roles' => [
        'super_admin' => 'Super Admin',
        'admin' => 'Admin',
        'department_manager' => 'Department Manager',
        'employee' => 'Employee',
        'hr_manager' => 'HR Manager',
        'legal_manager' => 'Legal Manager',
        'finance_manager' => 'Finance Manager',
        'auditor' => 'Auditor',
    ],

    // Permissions
    'permissions' => [
        'documents.view' => 'View Documents',
        'documents.create' => 'Create Documents',
        'documents.edit' => 'Edit Documents',
        'documents.delete' => 'Delete Documents',
        'documents.download' => 'Download Documents',
        'documents.share' => 'Share Documents',
        'documents.approve' => 'Approve Documents',
        'documents.view_confidential' => 'View Confidential Documents',
        'departments.view' => 'View Departments',
        'departments.create' => 'Create Departments',
        'departments.edit' => 'Edit Departments',
        'departments.delete' => 'Delete Departments',
        'categories.view' => 'View Categories',
        'categories.create' => 'Create Categories',
        'categories.edit' => 'Edit Categories',
        'categories.delete' => 'Delete Categories',
        'users.view' => 'View Users',
        'users.create' => 'Create Users',
        'users.edit' => 'Edit Users',
        'users.delete' => 'Delete Users',
        'users.manage_roles' => 'Manage Roles',
        'system.backup' => 'System Backup',
        'system.restore' => 'System Restore',
        'system.settings' => 'System Settings',
        'system.audit_logs' => 'Audit Logs',
        'system.reports' => 'Reports',
        'comments.view' => 'View Comments',
        'comments.create' => 'Create Comments',
        'comments.edit' => 'Edit Comments',
        'comments.delete' => 'Delete Comments',
    ],
];
