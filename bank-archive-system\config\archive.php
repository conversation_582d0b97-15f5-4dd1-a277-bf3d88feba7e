<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Archive System Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Bank Archive System.
    | You can modify these settings to customize the system behavior.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | System Information
    |--------------------------------------------------------------------------
    */
    'name' => env('ARCHIVE_SYSTEM_NAME', 'Bank Archive System'),
    'version' => '1.0.0',
    'description' => 'Professional Electronic Archive System for Banking Institutions',

    /*
    |--------------------------------------------------------------------------
    | File Upload Settings
    |--------------------------------------------------------------------------
    */
    'upload' => [
        'max_file_size' => env('ARCHIVE_MAX_FILE_SIZE', 10240), // KB (10MB default)
        'allowed_extensions' => [
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff',
            'txt', 'rtf', 'csv'
        ],
        'allowed_mime_types' => [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/tiff',
            'text/plain',
            'text/rtf',
            'text/csv'
        ],
        'storage_disk' => env('ARCHIVE_STORAGE_DISK', 'public'),
        'storage_path' => env('ARCHIVE_STORAGE_PATH', 'documents'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Document Settings
    |--------------------------------------------------------------------------
    */
    'documents' => [
        'auto_generate_number' => env('ARCHIVE_AUTO_GENERATE_NUMBER', true),
        'number_prefix' => env('ARCHIVE_NUMBER_PREFIX', 'DOC'),
        'number_format' => env('ARCHIVE_NUMBER_FORMAT', 'Y-m-d-'),
        'default_status' => env('ARCHIVE_DEFAULT_STATUS', 'active'),
        'require_approval_by_default' => env('ARCHIVE_REQUIRE_APPROVAL', false),
        'confidential_by_default' => env('ARCHIVE_CONFIDENTIAL_DEFAULT', false),
        'enable_versioning' => env('ARCHIVE_ENABLE_VERSIONING', true),
        'max_versions' => env('ARCHIVE_MAX_VERSIONS', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Settings
    |--------------------------------------------------------------------------
    */
    'search' => [
        'enable_full_text_search' => env('ARCHIVE_FULL_TEXT_SEARCH', true),
        'search_results_per_page' => env('ARCHIVE_SEARCH_RESULTS_PER_PAGE', 20),
        'enable_search_suggestions' => env('ARCHIVE_SEARCH_SUGGESTIONS', true),
        'max_search_history' => env('ARCHIVE_MAX_SEARCH_HISTORY', 50),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'enable_file_encryption' => env('ARCHIVE_ENABLE_ENCRYPTION', false),
        'encryption_algorithm' => env('ARCHIVE_ENCRYPTION_ALGORITHM', 'AES-256-CBC'),
        'enable_watermark' => env('ARCHIVE_ENABLE_WATERMARK', false),
        'watermark_text' => env('ARCHIVE_WATERMARK_TEXT', 'CONFIDENTIAL'),
        'enable_download_tracking' => env('ARCHIVE_DOWNLOAD_TRACKING', true),
        'max_download_attempts' => env('ARCHIVE_MAX_DOWNLOAD_ATTEMPTS', 5),
        'download_cooldown_minutes' => env('ARCHIVE_DOWNLOAD_COOLDOWN', 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Settings
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enable_auto_backup' => env('ARCHIVE_AUTO_BACKUP', true),
        'backup_frequency' => env('ARCHIVE_BACKUP_FREQUENCY', 'daily'), // daily, weekly, monthly
        'backup_time' => env('ARCHIVE_BACKUP_TIME', '02:00'),
        'backup_retention_days' => env('ARCHIVE_BACKUP_RETENTION', 30),
        'backup_storage_disk' => env('ARCHIVE_BACKUP_DISK', 'local'),
        'backup_notification_email' => env('ARCHIVE_BACKUP_EMAIL', null),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'enable_email_notifications' => env('ARCHIVE_EMAIL_NOTIFICATIONS', true),
        'enable_sms_notifications' => env('ARCHIVE_SMS_NOTIFICATIONS', false),
        'notify_on_document_upload' => env('ARCHIVE_NOTIFY_UPLOAD', true),
        'notify_on_document_approval' => env('ARCHIVE_NOTIFY_APPROVAL', true),
        'notify_on_document_expiry' => env('ARCHIVE_NOTIFY_EXPIRY', true),
        'expiry_notification_days' => env('ARCHIVE_EXPIRY_NOTIFICATION_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Audit Settings
    |--------------------------------------------------------------------------
    */
    'audit' => [
        'enable_activity_logging' => env('ARCHIVE_ACTIVITY_LOGGING', true),
        'log_user_actions' => env('ARCHIVE_LOG_USER_ACTIONS', true),
        'log_system_events' => env('ARCHIVE_LOG_SYSTEM_EVENTS', true),
        'log_file_access' => env('ARCHIVE_LOG_FILE_ACCESS', true),
        'audit_retention_days' => env('ARCHIVE_AUDIT_RETENTION', 365),
        'enable_audit_export' => env('ARCHIVE_AUDIT_EXPORT', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'enable_caching' => env('ARCHIVE_ENABLE_CACHING', true),
        'cache_duration' => env('ARCHIVE_CACHE_DURATION', 3600), // seconds
        'enable_compression' => env('ARCHIVE_ENABLE_COMPRESSION', true),
        'compression_quality' => env('ARCHIVE_COMPRESSION_QUALITY', 85),
        'enable_lazy_loading' => env('ARCHIVE_LAZY_LOADING', true),
        'pagination_size' => env('ARCHIVE_PAGINATION_SIZE', 25),
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        'enable_api' => env('ARCHIVE_ENABLE_API', false),
        'api_rate_limit' => env('ARCHIVE_API_RATE_LIMIT', 60), // requests per minute
        'enable_ldap' => env('ARCHIVE_ENABLE_LDAP', false),
        'ldap_server' => env('ARCHIVE_LDAP_SERVER', null),
        'enable_sso' => env('ARCHIVE_ENABLE_SSO', false),
        'sso_provider' => env('ARCHIVE_SSO_PROVIDER', null),
    ],

    /*
    |--------------------------------------------------------------------------
    | UI/UX Settings
    |--------------------------------------------------------------------------
    */
    'ui' => [
        'default_language' => env('ARCHIVE_DEFAULT_LANGUAGE', 'ar'),
        'supported_languages' => ['ar', 'en'],
        'enable_dark_mode' => env('ARCHIVE_ENABLE_DARK_MODE', true),
        'default_theme' => env('ARCHIVE_DEFAULT_THEME', 'light'),
        'enable_rtl' => env('ARCHIVE_ENABLE_RTL', true),
        'items_per_page' => env('ARCHIVE_ITEMS_PER_PAGE', 25),
        'enable_tooltips' => env('ARCHIVE_ENABLE_TOOLTIPS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Reporting Settings
    |--------------------------------------------------------------------------
    */
    'reports' => [
        'enable_advanced_reports' => env('ARCHIVE_ADVANCED_REPORTS', true),
        'enable_chart_exports' => env('ARCHIVE_CHART_EXPORTS', true),
        'default_export_format' => env('ARCHIVE_DEFAULT_EXPORT_FORMAT', 'pdf'),
        'supported_export_formats' => ['pdf', 'excel', 'csv'],
        'enable_scheduled_reports' => env('ARCHIVE_SCHEDULED_REPORTS', true),
        'max_report_data_points' => env('ARCHIVE_MAX_REPORT_DATA', 10000),
    ],

    /*
    |--------------------------------------------------------------------------
    | System Limits
    |--------------------------------------------------------------------------
    */
    'limits' => [
        'max_documents_per_user' => env('ARCHIVE_MAX_DOCS_PER_USER', null),
        'max_storage_per_user' => env('ARCHIVE_MAX_STORAGE_PER_USER', null), // MB
        'max_departments' => env('ARCHIVE_MAX_DEPARTMENTS', 100),
        'max_categories' => env('ARCHIVE_MAX_CATEGORIES', 200),
        'max_users' => env('ARCHIVE_MAX_USERS', 1000),
        'max_concurrent_uploads' => env('ARCHIVE_MAX_CONCURRENT_UPLOADS', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Settings
    |--------------------------------------------------------------------------
    */
    'maintenance' => [
        'enable_maintenance_mode' => env('ARCHIVE_MAINTENANCE_MODE', false),
        'maintenance_message_ar' => env('ARCHIVE_MAINTENANCE_MESSAGE_AR', 'النظام تحت الصيانة'),
        'maintenance_message_en' => env('ARCHIVE_MAINTENANCE_MESSAGE_EN', 'System under maintenance'),
        'auto_cleanup_temp_files' => env('ARCHIVE_AUTO_CLEANUP', true),
        'cleanup_frequency' => env('ARCHIVE_CLEANUP_FREQUENCY', 'daily'),
        'temp_file_retention_hours' => env('ARCHIVE_TEMP_RETENTION', 24),
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Settings
    |--------------------------------------------------------------------------
    */
    'development' => [
        'enable_debug_mode' => env('ARCHIVE_DEBUG_MODE', false),
        'enable_query_logging' => env('ARCHIVE_QUERY_LOGGING', false),
        'enable_profiling' => env('ARCHIVE_PROFILING', false),
        'fake_data_generation' => env('ARCHIVE_FAKE_DATA', false),
        'enable_test_routes' => env('ARCHIVE_TEST_ROUTES', false),
    ],
];
