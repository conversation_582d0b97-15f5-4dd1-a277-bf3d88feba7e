# دليل الأمان | Security Guide

## نظرة عامة | Overview

هذا الدليل يوضح الإجراءات الأمنية المطبقة في منظومة الأرشفة الإلكترونية وكيفية الحفاظ على أمان النظام.

This guide outlines the security measures implemented in the electronic archive system and how to maintain system security.

## الميزات الأمنية المطبقة | Implemented Security Features

### 1. المصادقة والتفويض | Authentication & Authorization

#### نظام الأدوار والصلاحيات | Role-Based Access Control (RBAC)
- **8 أدوار مختلفة** مع صلاحيات محددة لكل دور
- **40+ صلاحية مختلفة** تغطي جميع عمليات النظام
- **فصل الصلاحيات** بين الأقسام المختلفة
- **مبد<PERSON> الحد الأدنى من الصلاحيات** (Principle of Least Privilege)

#### حماية كلمات المرور | Password Protection
```php
// تشفير كلمات المرور باستخدام bcrypt
'password' => 'hashed',

// متطلبات كلمة المرور القوية
'password' => ['required', 'string', 'min:8', 'confirmed'],
```

### 2. حماية من الهجمات الشائعة | Common Attack Protection

#### حماية من SQL Injection
```php
// استخدام Eloquent ORM و Query Builder
Document::where('department_id', $departmentId)->get();

// استخدام Parameter Binding
DB::select('SELECT * FROM documents WHERE id = ?', [$id]);
```

#### حماية من XSS (Cross-Site Scripting)
```php
// تنظيف البيانات تلقائياً في Blade
{{ $document->title }} // آمن تلقائياً

// للبيانات غير المنظفة
{!! clean($html_content) !!}
```

#### حماية من CSRF (Cross-Site Request Forgery)
```html
<!-- رمز CSRF في جميع النماذج -->
@csrf

<!-- التحقق من الرمز في الخادم -->
<meta name="csrf-token" content="{{ csrf_token() }}">
```

### 3. تشفير البيانات | Data Encryption

#### تشفير قاعدة البيانات | Database Encryption
```php
// تشفير الحقول الحساسة
protected $casts = [
    'sensitive_data' => 'encrypted',
];
```

#### تشفير الملفات | File Encryption
```php
// تشفير الملفات المرفوعة (اختياري)
if (config('archive.security.enable_file_encryption')) {
    $encryptedContent = encrypt($fileContent);
}
```

### 4. تسجيل العمليات | Activity Logging

#### تتبع جميع العمليات | Comprehensive Activity Tracking
```php
// تسجيل تلقائي لجميع العمليات
use Spatie\Activitylog\Traits\LogsActivity;

// تسجيل مخصص للعمليات الحساسة
activity()
    ->performedOn($document)
    ->causedBy($user)
    ->withProperties(['action' => 'download'])
    ->log('Document downloaded');
```

### 5. التحكم في الوصول للملفات | File Access Control

#### حماية الملفات المرفوعة | Uploaded File Protection
```php
// التحقق من الصلاحيات قبل تحميل الملف
public function download(Document $document)
{
    $this->authorize('download', $document);
    
    // تسجيل عملية التحميل
    activity()->log('File downloaded');
    
    return response()->download($document->file_path);
}
```

#### فحص نوع الملفات | File Type Validation
```php
'file' => [
    'required',
    'file',
    'mimes:pdf,doc,docx,jpg,png',
    'max:10240' // 10MB
]
```

## إعدادات الأمان الموصى بها | Recommended Security Settings

### 1. إعدادات الخادم | Server Configuration

#### Apache/Nginx
```apache
# إخفاء معلومات الخادم
ServerTokens Prod
ServerSignature Off

# منع الوصول للملفات الحساسة
<Files ".env">
    Order allow,deny
    Deny from all
</Files>
```

#### SSL/TLS
```nginx
# فرض استخدام HTTPS
server {
    listen 80;
    return 301 https://$server_name$request_uri;
}

# إعدادات SSL قوية
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
```

### 2. إعدادات قاعدة البيانات | Database Configuration

#### MySQL Security
```sql
-- إنشاء مستخدم مخصص للتطبيق
CREATE USER 'archive_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON archive_db.* TO 'archive_user'@'localhost';

-- تشفير الاتصالات
require_secure_transport = ON
```

### 3. إعدادات Laravel | Laravel Configuration

#### Environment Variables
```env
# إعدادات الأمان
APP_DEBUG=false
APP_ENV=production

# مفاتيح قوية
APP_KEY=base64:your-32-character-secret-key

# إعدادات الجلسة الآمنة
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict
```

## إجراءات الأمان التشغيلية | Operational Security Procedures

### 1. إدارة المستخدمين | User Management

#### إنشاء المستخدمين | User Creation
```bash
# استخدام Artisan Command لإنشاء مستخدمين آمنين
php artisan make:user --role=employee --department=IT
```

#### مراجعة الصلاحيات | Permission Review
```php
// مراجعة دورية للصلاحيات
$users = User::with('roles.permissions')->get();
foreach ($users as $user) {
    // فحص الصلاحيات غير المستخدمة
    $unusedPermissions = $user->getUnusedPermissions();
}
```

### 2. مراقبة النظام | System Monitoring

#### مراقبة محاولات الدخول | Login Monitoring
```php
// تسجيل محاولات الدخول الفاشلة
Event::listen('auth.failed', function ($credentials) {
    Log::warning('Failed login attempt', [
        'email' => $credentials['email'],
        'ip' => request()->ip(),
        'user_agent' => request()->userAgent(),
    ]);
});
```

#### تنبيهات الأمان | Security Alerts
```php
// إرسال تنبيهات للعمليات الحساسة
if ($document->is_confidential && $action === 'download') {
    Mail::to($securityTeam)->send(new ConfidentialDocumentAccessed($document, $user));
}
```

### 3. النسخ الاحتياطي الآمن | Secure Backup

#### تشفير النسخ الاحتياطية | Backup Encryption
```bash
# نسخ احتياطي مشفر
php artisan backup:run --encrypt
```

#### تخزين آمن | Secure Storage
```php
// تخزين النسخ الاحتياطية في مواقع متعددة
'backup' => [
    'destination' => [
        'disks' => ['s3', 'local_encrypted']
    ]
]
```

## فحص الثغرات الأمنية | Security Vulnerability Assessment

### 1. فحص التبعيات | Dependency Scanning
```bash
# فحص ثغرات PHP
composer audit

# فحص ثغرات JavaScript
npm audit

# إصلاح الثغرات
composer update
npm update
```

### 2. فحص الكود | Code Security Scanning
```bash
# استخدام PHPStan للتحليل الثابت
./vendor/bin/phpstan analyse

# فحص أمني متقدم
composer require --dev roave/security-advisories
```

### 3. اختبار الاختراق | Penetration Testing
```bash
# اختبار الحقن SQL
sqlmap -u "http://archive.bank.com/search" --data="query=test"

# اختبار XSS
# استخدام أدوات مثل OWASP ZAP
```

## خطة الاستجابة للحوادث | Incident Response Plan

### 1. اكتشاف الحادث | Incident Detection
- **مراقبة مستمرة** لسجلات النظام
- **تنبيهات تلقائية** للأنشطة المشبوهة
- **فحص دوري** للملفات والصلاحيات

### 2. الاستجابة الفورية | Immediate Response
```bash
# عزل النظام
php artisan down --message="Security maintenance"

# تغيير كلمات المرور
php artisan user:reset-passwords --force

# مراجعة السجلات
php artisan log:security-review
```

### 3. التعافي | Recovery
- **استعادة من النسخ الاحتياطية** الآمنة
- **تحديث النظام** وإصلاح الثغرات
- **مراجعة الصلاحيات** وإعادة تعيينها

## قائمة فحص الأمان | Security Checklist

### ✅ قبل النشر | Pre-Deployment
- [ ] تحديث جميع التبعيات
- [ ] فحص الثغرات الأمنية
- [ ] تشفير البيانات الحساسة
- [ ] إعداد SSL/TLS
- [ ] تكوين جدار الحماية
- [ ] إعداد النسخ الاحتياطي

### ✅ بعد النشر | Post-Deployment
- [ ] تفعيل مراقبة النظام
- [ ] إعداد التنبيهات الأمنية
- [ ] اختبار الصلاحيات
- [ ] مراجعة السجلات
- [ ] تدريب المستخدمين

### ✅ الصيانة الدورية | Regular Maintenance
- [ ] تحديث النظام شهرياً
- [ ] مراجعة الصلاحيات ربع سنوياً
- [ ] اختبار النسخ الاحتياطية شهرياً
- [ ] تدقيق أمني سنوياً

## جهات الاتصال الأمنية | Security Contacts

### فريق الأمان الداخلي | Internal Security Team
- **مدير الأمان:** <EMAIL>
- **فريق الدعم التقني:** <EMAIL>
- **الطوارئ:** +966-xxx-xxxx

### الإبلاغ عن الثغرات | Vulnerability Reporting
إذا اكتشفت ثغرة أمنية، يرجى الإبلاغ عنها فوراً عبر:
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966-xxx-xxxx (متاح 24/7)

---

**تذكر:** الأمان مسؤولية الجميع. تأكد من اتباع جميع الإجراءات الأمنية والإبلاغ عن أي نشاط مشبوه فوراً.

**Remember:** Security is everyone's responsibility. Make sure to follow all security procedures and report any suspicious activity immediately.
