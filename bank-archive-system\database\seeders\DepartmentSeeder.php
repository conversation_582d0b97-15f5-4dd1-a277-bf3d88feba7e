<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Department;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $departments = [
            [
                'name_ar' => 'الإدارة العامة',
                'name_en' => 'General Management',
                'description_ar' => 'الإدارة العليا للمؤسسة المصرفية',
                'description_en' => 'Top management of the banking institution',
                'code' => 'GM',
                'is_active' => true,
            ],
            [
                'name_ar' => 'الموارد البشرية',
                'name_en' => 'Human Resources',
                'description_ar' => 'إدارة شؤون الموظفين والتوظيف',
                'description_en' => 'Employee affairs and recruitment management',
                'code' => 'HR',
                'is_active' => true,
            ],
            [
                'name_ar' => 'الشؤون القانونية',
                'name_en' => 'Legal Affairs',
                'description_ar' => 'الشؤون القانونية والامتثال',
                'description_en' => 'Legal affairs and compliance',
                'code' => 'LEGAL',
                'is_active' => true,
            ],
            [
                'name_ar' => 'الشؤون المالية',
                'name_en' => 'Finance',
                'description_ar' => 'الإدارة المالية والمحاسبة',
                'description_en' => 'Financial management and accounting',
                'code' => 'FIN',
                'is_active' => true,
            ],
            [
                'name_ar' => 'تقنية المعلومات',
                'name_en' => 'Information Technology',
                'description_ar' => 'إدارة الأنظمة والتقنية',
                'description_en' => 'Systems and technology management',
                'code' => 'IT',
                'is_active' => true,
            ],
            [
                'name_ar' => 'المخاطر والامتثال',
                'name_en' => 'Risk & Compliance',
                'description_ar' => 'إدارة المخاطر والامتثال التنظيمي',
                'description_en' => 'Risk management and regulatory compliance',
                'code' => 'RISK',
                'is_active' => true,
            ],
            [
                'name_ar' => 'العمليات المصرفية',
                'name_en' => 'Banking Operations',
                'description_ar' => 'العمليات المصرفية اليومية',
                'description_en' => 'Daily banking operations',
                'code' => 'OPS',
                'is_active' => true,
            ],
            [
                'name_ar' => 'خدمة العملاء',
                'name_en' => 'Customer Service',
                'description_ar' => 'خدمة ودعم العملاء',
                'description_en' => 'Customer service and support',
                'code' => 'CS',
                'is_active' => true,
            ],
            [
                'name_ar' => 'التسويق',
                'name_en' => 'Marketing',
                'description_ar' => 'التسويق والعلاقات العامة',
                'description_en' => 'Marketing and public relations',
                'code' => 'MKT',
                'is_active' => true,
            ],
            [
                'name_ar' => 'التدقيق الداخلي',
                'name_en' => 'Internal Audit',
                'description_ar' => 'التدقيق الداخلي والمراجعة',
                'description_en' => 'Internal audit and review',
                'code' => 'AUDIT',
                'is_active' => true,
            ],
        ];

        foreach ($departments as $department) {
            Department::create($department);
        }
    }
}
