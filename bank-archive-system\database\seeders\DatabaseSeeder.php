<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run seeders in order
        $this->call([
            RolePermissionSeeder::class,
            DepartmentSeeder::class,
            DocumentCategorySeeder::class,
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'employee_id' => 'EMP001',
            'department_id' => 1, // General Management
            'phone' => '+966501234567',
            'position_ar' => 'مدير النظام',
            'position_en' => 'System Administrator',
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);
        $admin->assignRole('super_admin');

        // Create HR Manager
        $hrManager = User::create([
            'name' => 'مدير الموارد البشرية',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'employee_id' => 'EMP002',
            'department_id' => 2, // HR
            'phone' => '+966501234568',
            'position_ar' => 'مدير الموارد البشرية',
            'position_en' => 'HR Manager',
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);
        $hrManager->assignRole('hr_manager');

        // Create Legal Manager
        $legalManager = User::create([
            'name' => 'مدير الشؤون القانونية',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'employee_id' => 'EMP003',
            'department_id' => 3, // Legal
            'phone' => '+966501234569',
            'position_ar' => 'مدير الشؤون القانونية',
            'position_en' => 'Legal Manager',
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);
        $legalManager->assignRole('legal_manager');

        // Create Finance Manager
        $financeManager = User::create([
            'name' => 'مدير الشؤون المالية',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'employee_id' => 'EMP004',
            'department_id' => 4, // Finance
            'phone' => '+966501234570',
            'position_ar' => 'مدير الشؤون المالية',
            'position_en' => 'Finance Manager',
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);
        $financeManager->assignRole('finance_manager');

        // Create regular employee
        $employee = User::create([
            'name' => 'موظف عادي',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'employee_id' => 'EMP005',
            'department_id' => 5, // IT
            'phone' => '+966501234571',
            'position_ar' => 'موظف',
            'position_en' => 'Employee',
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);
        $employee->assignRole('employee');
    }
}
