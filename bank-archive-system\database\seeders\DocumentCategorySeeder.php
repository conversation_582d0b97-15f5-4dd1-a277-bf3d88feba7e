<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DocumentCategory;

class DocumentCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name_ar' => 'السياسات والإجراءات',
                'name_en' => 'Policies & Procedures',
                'description_ar' => 'وثائق السياسات والإجراءات المؤسسية',
                'description_en' => 'Institutional policies and procedures documents',
                'code' => 'POLICY',
                'color' => '#3B82F6',
                'is_active' => true,
            ],
            [
                'name_ar' => 'العقود والاتفاقيات',
                'name_en' => 'Contracts & Agreements',
                'description_ar' => 'العقود والاتفاقيات القانونية',
                'description_en' => 'Legal contracts and agreements',
                'code' => 'CONTRACT',
                'color' => '#10B981',
                'is_active' => true,
            ],
            [
                'name_ar' => 'التقارير المالية',
                'name_en' => 'Financial Reports',
                'description_ar' => 'التقارير والبيانات المالية',
                'description_en' => 'Financial reports and statements',
                'code' => 'FINREP',
                'color' => '#F59E0B',
                'is_active' => true,
            ],
            [
                'name_ar' => 'وثائق الموظفين',
                'name_en' => 'Employee Documents',
                'description_ar' => 'وثائق ومستندات الموظفين',
                'description_en' => 'Employee documents and records',
                'code' => 'EMPLOYEE',
                'color' => '#8B5CF6',
                'is_active' => true,
            ],
            [
                'name_ar' => 'التراخيص والتصاريح',
                'name_en' => 'Licenses & Permits',
                'description_ar' => 'التراخيص والتصاريح الرسمية',
                'description_en' => 'Official licenses and permits',
                'code' => 'LICENSE',
                'color' => '#EF4444',
                'is_active' => true,
            ],
            [
                'name_ar' => 'وثائق الامتثال',
                'name_en' => 'Compliance Documents',
                'description_ar' => 'وثائق الامتثال التنظيمي',
                'description_en' => 'Regulatory compliance documents',
                'code' => 'COMPLIANCE',
                'color' => '#06B6D4',
                'is_active' => true,
            ],
            [
                'name_ar' => 'التدقيق والمراجعة',
                'name_en' => 'Audit & Review',
                'description_ar' => 'تقارير التدقيق والمراجعة',
                'description_en' => 'Audit and review reports',
                'code' => 'AUDIT',
                'color' => '#84CC16',
                'is_active' => true,
            ],
            [
                'name_ar' => 'وثائق العملاء',
                'name_en' => 'Customer Documents',
                'description_ar' => 'وثائق ومستندات العملاء',
                'description_en' => 'Customer documents and records',
                'code' => 'CUSTOMER',
                'color' => '#F97316',
                'is_active' => true,
            ],
            [
                'name_ar' => 'التقارير التشغيلية',
                'name_en' => 'Operational Reports',
                'description_ar' => 'التقارير التشغيلية اليومية',
                'description_en' => 'Daily operational reports',
                'code' => 'OPREP',
                'color' => '#EC4899',
                'is_active' => true,
            ],
            [
                'name_ar' => 'وثائق أخرى',
                'name_en' => 'Other Documents',
                'description_ar' => 'وثائق ومستندات متنوعة',
                'description_en' => 'Miscellaneous documents',
                'code' => 'OTHER',
                'color' => '#6B7280',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            DocumentCategory::create($category);
        }
    }
}
