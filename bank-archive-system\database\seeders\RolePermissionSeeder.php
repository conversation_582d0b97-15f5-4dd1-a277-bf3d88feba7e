<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Document permissions
            'documents.view',
            'documents.create',
            'documents.edit',
            'documents.delete',
            'documents.download',
            'documents.share',
            'documents.approve',
            'documents.view_confidential',

            // Department permissions
            'departments.view',
            'departments.create',
            'departments.edit',
            'departments.delete',

            // Category permissions
            'categories.view',
            'categories.create',
            'categories.edit',
            'categories.delete',

            // User permissions
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',
            'users.manage_roles',

            // System permissions
            'system.backup',
            'system.restore',
            'system.settings',
            'system.audit_logs',
            'system.reports',

            // Comment permissions
            'comments.view',
            'comments.create',
            'comments.edit',
            'comments.delete',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - Full access
        $superAdmin = Role::create(['name' => 'super_admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - Most permissions except system critical ones
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo([
            'documents.view', 'documents.create', 'documents.edit', 'documents.delete',
            'documents.download', 'documents.share', 'documents.approve', 'documents.view_confidential',
            'departments.view', 'departments.create', 'departments.edit', 'departments.delete',
            'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
            'users.view', 'users.create', 'users.edit', 'users.delete',
            'system.reports', 'system.audit_logs',
            'comments.view', 'comments.create', 'comments.edit', 'comments.delete',
        ]);

        // Department Manager - Department specific permissions
        $deptManager = Role::create(['name' => 'department_manager']);
        $deptManager->givePermissionTo([
            'documents.view', 'documents.create', 'documents.edit', 'documents.delete',
            'documents.download', 'documents.share', 'documents.approve',
            'departments.view',
            'categories.view',
            'users.view',
            'comments.view', 'comments.create', 'comments.edit', 'comments.delete',
        ]);

        // Employee - Basic permissions
        $employee = Role::create(['name' => 'employee']);
        $employee->givePermissionTo([
            'documents.view', 'documents.create', 'documents.download',
            'departments.view',
            'categories.view',
            'comments.view', 'comments.create',
        ]);

        // HR Manager - HR specific permissions
        $hrManager = Role::create(['name' => 'hr_manager']);
        $hrManager->givePermissionTo([
            'documents.view', 'documents.create', 'documents.edit', 'documents.delete',
            'documents.download', 'documents.share', 'documents.approve', 'documents.view_confidential',
            'departments.view',
            'categories.view',
            'users.view', 'users.create', 'users.edit', 'users.delete',
            'comments.view', 'comments.create', 'comments.edit', 'comments.delete',
        ]);

        // Legal Manager - Legal specific permissions
        $legalManager = Role::create(['name' => 'legal_manager']);
        $legalManager->givePermissionTo([
            'documents.view', 'documents.create', 'documents.edit', 'documents.delete',
            'documents.download', 'documents.share', 'documents.approve', 'documents.view_confidential',
            'departments.view',
            'categories.view',
            'users.view',
            'comments.view', 'comments.create', 'comments.edit', 'comments.delete',
        ]);

        // Finance Manager - Finance specific permissions
        $financeManager = Role::create(['name' => 'finance_manager']);
        $financeManager->givePermissionTo([
            'documents.view', 'documents.create', 'documents.edit', 'documents.delete',
            'documents.download', 'documents.share', 'documents.approve', 'documents.view_confidential',
            'departments.view',
            'categories.view',
            'users.view',
            'system.reports',
            'comments.view', 'comments.create', 'comments.edit', 'comments.delete',
        ]);

        // Auditor - Read-only access with audit capabilities
        $auditor = Role::create(['name' => 'auditor']);
        $auditor->givePermissionTo([
            'documents.view', 'documents.download', 'documents.view_confidential',
            'departments.view',
            'categories.view',
            'users.view',
            'system.audit_logs', 'system.reports',
            'comments.view',
        ]);
    }
}
